package com.mediacomm.caesar.integration;

import com.mediacomm.caesar.controller.CaesarCmdServer;
import com.mediacomm.caesar.domain.CaesarMqRequest;
import com.mediacomm.caesar.domain.CaesarPanelRect;
import com.mediacomm.caesar.domain.CaesarVideoPanels;
import com.mediacomm.entity.Result;
import com.mediacomm.entity.message.reqeust.body.PanelRectRequestBody;
import com.mediacomm.system.variable.RoutingKey;
import com.mediacomm.util.JsonUtils;
import feign.FeignException;
import java.net.URI;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Caesar KVM OPEN_VW_PANEL 集成测试
 * 测试从RabbitMQ消息接收到Caesar服务调用的完整流程
 */
public class CaesarOpenVwPanelIT extends BaseIntegrationTest {

    @Test
    void testOpenVwPanelSuccess() throws InterruptedException {
        // 准备测试数据
        CaesarMqRequest<PanelRectRequestBody> request = TestDataFactory.createTestCaesarMqRequest();
        String messageBody = JsonUtils.encode(request);

        // 配置Caesar服务模拟 - 成功响应
        when(caesarFeignClientApi.getVideoWallPanels(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID)))
                .thenReturn(new CaesarVideoPanels());
        when(caesarFeignClientApi.openVideoWallPanel(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID),
            any(CaesarPanelRect.class), eq(TestDataFactory.TEST_USER_ADDRESS), eq(TestDataFactory.TEST_USER_NAME))).thenReturn(
            TestDataFactory.createSuccessfulCaesarResponse());
        // 发送RabbitMQ消息
        String result = sendMessageAndWaitForResponse(messageBody, RoutingKey.CAESAR_KVM_OPEN_VW_PANEL);

        // 验证结果
        assertNotNull(result, "响应不应为空");
        
        Result<?> response = JsonUtils.decode(result, Result.class);
        assertNotNull(response, "响应应该是有效的Result对象");
        assertEquals(200, response.getCode(), "响应码应该是200");
        verify(caesarFeignClientApi, times(1)).openVideoWallPanel(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID),
            any(CaesarPanelRect.class), eq(TestDataFactory.TEST_USER_ADDRESS), eq(TestDataFactory.TEST_USER_NAME));
    }

    @Test
    void testOpenVwPanelCaesarFailure() throws InterruptedException {
        // 准备测试数据
        CaesarMqRequest<PanelRectRequestBody> request = TestDataFactory.createTestCaesarMqRequest();
        String messageBody = JsonUtils.encode(request);
        // 配置Caesar服务模拟 - 失败响应
        when(caesarFeignClientApi.getVideoWallPanels(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID)))
            .thenReturn(new CaesarVideoPanels());
        when(caesarFeignClientApi.openVideoWallPanel(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID),
            any(CaesarPanelRect.class), eq(TestDataFactory.TEST_USER_ADDRESS), eq(TestDataFactory.TEST_USER_NAME))).thenReturn(
            TestDataFactory.createFailedCaesarResponse());
        // 发送RabbitMQ消息
        String result = sendMessageAndWaitForResponse(messageBody, RoutingKey.CAESAR_KVM_OPEN_VW_PANEL);

        // 验证结果
        assertNotNull(result, "响应不应为空");
        
        Result<?> response = JsonUtils.decode(result, Result.class);
        assertNotNull(response, "响应应该是有效的Result对象");
        assertEquals(500, response.getCode(), "响应码应该是500");
    }

    @Test
    void testOpenVwPanelInvalidMasterId() throws InterruptedException {
        // 准备测试数据 - 使用无效的主机ID
        CaesarMqRequest<PanelRectRequestBody> request = TestDataFactory.createTestCaesarMqRequest();
        request.setMasterId("invalid-master-id");
        String messageBody = JsonUtils.encode(request);

        // 发送RabbitMQ消息
        String result = sendMessageAndWaitForResponse(messageBody, RoutingKey.CAESAR_KVM_OPEN_VW_PANEL);

        // 验证结果
        assertNotNull(result, "响应不应为空");
        
        Result<?> response = JsonUtils.decode(result, Result.class);
        assertNotNull(response, "响应应该是有效的Result对象");
        assertEquals(404, response.getCode(), "响应码应该是404");
        assertTrue(response.getMessage().contains(CaesarCmdServer.NO_HOST), "错误消息应该包含'主机不存在'");

        // 验证Caesar服务没有被调用
        verify(caesarFeignClientApi, times(0)).openVideoWallPanel(any(URI.class), anyInt(),
            any(CaesarPanelRect.class), anyString(), anyString());
    }

    @Test
    void testOpenVwPanelInvalidMessageFormat() throws InterruptedException {
        // 发送无效的JSON消息
        String invalidMessage = "{invalid json}";

        // 发送RabbitMQ消息
        String result = sendMessageAndWaitForResponse(invalidMessage, RoutingKey.CAESAR_KVM_OPEN_VW_PANEL);

        // 验证结果
        assertNotNull(result, "响应不应为空");
        
        Result<?> response = JsonUtils.decode(result, Result.class);
        assertNotNull(response, "响应应该是有效的Result对象");
        assertEquals(400, response.getCode(), "响应码应该是400");
        assertTrue(response.getMessage().contains(CaesarCmdServer.PARAM_ERR), "错误消息应该包含JSON解析错误信息");
        // 验证Caesar服务没有被调用
        verify(caesarFeignClientApi, times(0)).openVideoWallPanel(any(URI.class), anyInt(),
            any(CaesarPanelRect.class), anyString(), anyString());
    }

    @Test
    void testOpenVwPanelNetworkError() throws InterruptedException {
        // 准备测试数据
        CaesarMqRequest<PanelRectRequestBody> request = TestDataFactory.createTestCaesarMqRequest();
        String messageBody = JsonUtils.encode(request);

        // 配置Caesar服务模拟 - 网络错误
        when(caesarFeignClientApi.getVideoWallPanels(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID)))
            .thenReturn(new CaesarVideoPanels());
        when(caesarFeignClientApi.openVideoWallPanel(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID),
            any(CaesarPanelRect.class), eq(TestDataFactory.TEST_USER_ADDRESS), eq(TestDataFactory.TEST_USER_NAME)))
            .thenThrow(FeignException.class);
        // 发送RabbitMQ消息
        String result = sendMessageAndWaitForResponse(messageBody, RoutingKey.CAESAR_KVM_OPEN_VW_PANEL);

        // 验证结果
        assertNotNull(result, "响应不应为空");

        // 网络错误应该被处理并返回适当的错误响应
        Result<?> response = JsonUtils.decode(result, Result.class);
        assertNotNull(response, "响应应该是有效的Result对象");
        assertEquals(500, response.getCode(), "响应码应该是500");

        // 验证Caesar服务被调用
        verify(caesarFeignClientApi, times(1)).openVideoWallPanel(any(URI.class), eq(TestDataFactory.TEST_VIDEO_WALL_ID),
            any(CaesarPanelRect.class), eq(TestDataFactory.TEST_USER_ADDRESS), eq(TestDataFactory.TEST_USER_NAME));
    }

    /**
     * 发送RabbitMQ消息并等待响应
     */
    private String sendMessageAndWaitForResponse(String messageBody, String routingKey) throws InterruptedException {
        // 创建消息属性
        MessageProperties properties = new MessageProperties();
        properties.setReceivedRoutingKey(routingKey);
        
        // 创建消息
        Message message = new Message(messageBody.getBytes(), properties);
        
        // 直接调用CaesarRunner的receiver方法模拟消息处理
        return caesarRunner.receiver(messageBody, null, routingKey);
    }
}
