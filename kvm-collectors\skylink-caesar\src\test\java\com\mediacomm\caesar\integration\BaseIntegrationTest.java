package com.mediacomm.caesar.integration;

import com.mediacomm.caesar.CaesarRunner;
import com.mediacomm.caesar.controller.CaesarFeignClientApi;
import com.mediacomm.caesar.controller.Vp7FeignClientApi;
import com.mediacomm.entity.dao.KvmAsset;
import com.mediacomm.entity.dao.KvmMaster;
import com.mediacomm.entity.dao.KvmVideoWall;
import com.mediacomm.system.service.KvmAssetService;
import com.mediacomm.system.service.KvmMasterService;
import com.mediacomm.system.service.KvmVideoWallService;
import com.mediacomm.util.RedisUtil;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.TestPropertySource;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.MySQLContainer;
import org.testcontainers.containers.RabbitMQContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

/**
 * 集成测试基类
 * 配置Testcontainers、WireMock和测试数据初始化
 */
@SpringBootTest(webEnvironment = WebEnvironment.NONE, classes = {
    com.mediacomm.caesar.integration.CaesarIntegrationTestConfig.class})
@ActiveProfiles("dev")
@Testcontainers
@MapperScan("com.mediacomm.system.mapper")
@TestPropertySource(properties = {
    "logging.level.com.mediacomm.caesar=DEBUG",
    "spring.rabbitmq.listener.simple.auto-startup=true",
    "spring.mqtt.url=tcp://localhost:1883",
    "spring.mqtt.client.id=test-client",
    "spring.mqtt.default.topic=test",
    "spring.mqtt.userName=test",
    "spring.mqtt.password=test"
})
public abstract class BaseIntegrationTest {
    
    // MySQL容器
    @Container
    static MySQLContainer<?> mysql = new MySQLContainer<>(DockerImageName.parse("mysql:8.0.33"))
            .withDatabaseName("skylink")
            .withUsername("root")
            .withPassword("123456")
            .withEnv("MYSQL_ROOT_PASSWORD", "123456"); 

    // Redis容器
    @Container
    static GenericContainer<?> redis = new GenericContainer<>(DockerImageName.parse("redis:7.2.0-alpine"))
            .withExposedPorts(6379);

    // RabbitMQ容器
    @Container
    static RabbitMQContainer rabbitmq = new RabbitMQContainer(DockerImageName.parse("rabbitmq:3.12-management"))
            .withUser("test", "test")
            .withVhost("/");

    @Autowired
    protected KvmMasterService kvmMasterService;

    @Autowired
    protected KvmVideoWallService kvmVideoWallService;

    @Autowired
    protected KvmAssetService kvmAssetService;

    @Autowired
    protected RabbitTemplate rabbitTemplate;

    @Autowired
    protected RedisUtil redisUtil;

    @Autowired
    protected CaesarRunner caesarRunner;

    @MockBean
    protected CaesarFeignClientApi caesarFeignClientApi;

    @MockBean
    protected Vp7FeignClientApi vp7FeignClientApi;

    /**
     * 动态配置测试容器的连接属性
     */
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        // MySQL配置
        registry.add("spring.datasource.url", mysql::getJdbcUrl);
        registry.add("spring.datasource.username", mysql::getUsername);
        registry.add("spring.datasource.password", mysql::getPassword);

        // Redis配置
        registry.add("spring.data.redis.host", redis::getHost);
        registry.add("spring.data.redis.port", () -> redis.getMappedPort(6379));

        // RabbitMQ配置
        registry.add("spring.rabbitmq.host", rabbitmq::getHost);
        registry.add("spring.rabbitmq.port", () -> rabbitmq.getMappedPort(5672));
        registry.add("spring.rabbitmq.username", rabbitmq::getAdminUsername);
        registry.add("spring.rabbitmq.password", rabbitmq::getAdminPassword);
    }

    @BeforeAll
    static void setUpAll() {
    }

    @AfterAll
    static void tearDownAll() {
    }

    @BeforeEach
    void setUp() {
        // 清理Redis
        redisUtil.clear();
        
        // 初始化测试数据
        clearTestData();
        initTestData();
    }
    
    /**
     * 初始化测试数据
     */
    protected void initTestData() {
        // 创建测试主机
        KvmMaster master = TestDataFactory.createTestKvmMaster();
        kvmMasterService.save(master);

        // 创建测试视频墙
        KvmVideoWall videoWall = TestDataFactory.createTestKvmVideoWall();
        kvmVideoWallService.save(videoWall);

        // 创建测试资产
        KvmAsset asset = TestDataFactory.createTestKvmAsset();
        kvmAssetService.save(asset);
    }

    /**
     * 清理测试数据
     */
    protected void clearTestData() {
        kvmAssetService.clearAll();
        kvmVideoWallService.clearAll();
        kvmMasterService.clearAll();
    }
}
